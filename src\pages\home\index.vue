<template>
  <div class="home-page">
    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchValue"
        placeholder="搜索项目、区域、商圈、地标"
        background="#f5f5f5"
        shape="round"
        @search="onSearch"
      />
      <div class="location-btn" @click="onLocation">
        <van-icon name="location-o" size="20" color="#ff6b35" />
        <span>地图</span>
      </div>
    </div>

    <!-- 保障专题 -->
    <div class="guarantee-section">
      <h3 class="section-title">保障专题</h3>
      <div class="guarantee-grid">
        <div
          v-for="item in guaranteeItems"
          :key="item.id"
          class="guarantee-item"
          :style="{ backgroundColor: item.color }"
          @click="onGuaranteeClick(item)"
        >
          <van-icon :name="item.icon" size="24" color="white" />
          <span class="guarantee-text">{{ item.title }}</span>
        </div>
      </div>
    </div>

    <!-- 功能图标网格 -->
    <div class="function-section">
      <div class="function-grid">
        <div
          v-for="item in functionItems"
          :key="item.id"
          class="function-item"
          @click="onFunctionClick(item)"
        >
          <div class="function-icon">
            <van-icon :name="item.icon" size="24" :color="item.color" />
          </div>
          <span class="function-text">{{ item.title }}</span>
        </div>
      </div>
    </div>

    <!-- 底部功能区 -->
    <div class="bottom-function-section">
      <div class="bottom-function-grid">
        <div
          v-for="item in bottomFunctionItems"
          :key="item.id"
          class="bottom-function-item"
          @click="onBottomFunctionClick(item)"
        >
          <div class="bottom-function-icon">
            <van-icon :name="item.icon" size="20" color="#666" />
          </div>
          <span class="bottom-function-text">{{ item.title }}</span>
        </div>
      </div>
    </div>

    <!-- 正在报名 -->
    <div class="registration-section">
      <div class="section-header">
        <van-icon name="fire-o" size="16" color="#ff6b35" />
        <span class="registration-title">正在报名</span>
      </div>
      <div class="registration-content">
        <div class="registration-item">
          <div class="registration-info">
            <h4>成都绿地中心111</h4>
            <p class="registration-desc">房源数量：1</p>
          </div>
          <van-button
            type="danger"
            size="small"
            round
            @click="onRegistration"
          >
            我要报名
          </van-button>
        </div>
      </div>
    </div>

    <!-- 底部客服按钮 -->
    <div class="customer-service">
      <van-button
        type="danger"
        round
        icon="service-o"
        @click="onCustomerService"
      >
        联系客服
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()
const searchValue = ref('')

// 保障专题数据
const guaranteeItems = ref([
  { id: 1, title: '公共租房', icon: 'home-o', color: '#ff6b35' },
  { id: 2, title: '租房补贴', icon: 'gold-coin-o', color: '#ffa726' },
  { id: 3, title: '保障性住房', icon: 'shield-o', color: '#42a5f5' },
  { id: 4, title: '保障性社区', icon: 'cluster-o', color: '#ab47bc' },
  { id: 5, title: '人才安居', icon: 'user-o', color: '#66bb6a' }
])

// 功能图标数据
const functionItems = ref([
  { id: 1, title: '资格申请', icon: 'edit', color: '#ff6b35' },
  { id: 2, title: '我要找房', icon: 'search', color: '#42a5f5' },
  { id: 3, title: '我要买房', icon: 'home-o', color: '#66bb6a' },
  { id: 4, title: '我要补贴', icon: 'gold-coin-o', color: '#ffa726' },
  { id: 5, title: '报名登记', icon: 'add-square', color: '#ab47bc' }
])

// 底部功能数据
const bottomFunctionItems = ref([
  { id: 1, title: '在线选房', icon: 'home-o' },
  { id: 2, title: '在线缴费', icon: 'gold-coin-o' },
  { id: 3, title: '政策文件', icon: 'description' },
  { id: 4, title: '下载文件', icon: 'down' },
  { id: 5, title: '看房预约', icon: 'calendar-o' }
])

// 搜索功能
const onSearch = (value) => {
  showToast(`搜索: ${value}`)
}

// 地图定位
const onLocation = () => {
  showToast('打开地图定位')
}

// 保障专题点击
const onGuaranteeClick = (item) => {
  showToast(`点击了: ${item.title}`)
}

// 功能图标点击
const onFunctionClick = (item) => {
  showToast(`点击了: ${item.title}`)
}

// 底部功能点击
const onBottomFunctionClick = (item) => {
  showToast(`点击了: ${item.title}`)
}

// 报名功能
const onRegistration = () => {
  showToast('跳转到报名页面')
}

// 客服功能
const onCustomerService = () => {
  showToast('联系客服')
}
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 20px;
}

// 搜索栏样式
.search-section {
  display: flex;
  align-items: center;
  padding: 16px;
  background: white;
  gap: 12px;

  .van-search {
    flex: 1;
  }

  .location-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 8px;
    cursor: pointer;

    span {
      font-size: 12px;
      color: #ff6b35;
    }
  }
}

// 保障专题样式
.guarantee-section {
  margin-top: 12px;
  background: white;
  padding: 16px;

  .section-title {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .guarantee-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 12px;
  }

  .guarantee-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px 8px;
    border-radius: 12px;
    cursor: pointer;
    transition: transform 0.2s;

    &:active {
      transform: scale(0.95);
    }

    .guarantee-text {
      margin-top: 8px;
      font-size: 12px;
      color: white;
      font-weight: 500;
      text-align: center;
    }
  }
}

// 功能图标样式
.function-section {
  margin-top: 12px;
  background: white;
  padding: 16px;

  .function-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 16px;
  }

  .function-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: transform 0.2s;

    &:active {
      transform: scale(0.95);
    }

    .function-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      background: #f8f9fa;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
    }

    .function-text {
      font-size: 12px;
      color: #333;
      text-align: center;
    }
  }
}

// 底部功能区样式
.bottom-function-section {
  margin-top: 12px;
  background: white;
  padding: 16px;

  .bottom-function-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 16px;
  }

  .bottom-function-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: transform 0.2s;

    &:active {
      transform: scale(0.95);
    }

    .bottom-function-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: #f8f9fa;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 6px;
    }

    .bottom-function-text {
      font-size: 11px;
      color: #666;
      text-align: center;
    }
  }
}

// 正在报名样式
.registration-section {
  margin-top: 12px;
  background: white;
  padding: 16px;

  .section-header {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 16px;

    .registration-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }

  .registration-content {
    .registration-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 12px;

      .registration-info {
        flex: 1;

        h4 {
          margin: 0 0 4px 0;
          font-size: 16px;
          color: #333;
        }

        .registration-desc {
          margin: 0;
          font-size: 14px;
          color: #666;
        }
      }
    }
  }
}

// 客服按钮样式
.customer-service {
  position: fixed;
  bottom: 80px;
  right: 16px;
  z-index: 100;
}
</style>

<route lang="json5">
{
  name: 'Home',
  meta: {
    title: '首页'
  }
}
</route>